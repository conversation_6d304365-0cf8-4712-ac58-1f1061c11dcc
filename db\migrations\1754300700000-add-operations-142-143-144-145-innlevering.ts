import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperations142143144145Innlevering1754300700000 implements MigrationInterface {
    name = 'AddOperations142143144145Innlevering1754300700000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operations 142-145
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (142, 'Innlevering', 'Filen ble innlevert for del 1 via siden "Lever for kandidat". Filen ble innlevert for både del 1 og del 2.'),
            (143, 'Innlevering', 'Filen ble innlevert for del 2 via siden "Lever for kandidat". Filen ble innlevert for både del 1 og del 2.'),
            (144, 'Innlevering', 'Filen ble innlevert for del 1 via Gruppeopplasteren. Filen ble innlevert for både del 1 og del 2.'),
            (145, 'Innlevering', '<PERSON><PERSON> ble innlevert for del 2 via Gruppeopplasteren. Filen ble innlevert for både del 1 og del 2.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operations
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" IN (142, 143, 144, 145);
        `);
    }
}
