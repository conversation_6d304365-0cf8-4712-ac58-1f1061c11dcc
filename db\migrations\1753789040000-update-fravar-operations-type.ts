import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFravarOperationsType1753789040000 implements MigrationInterface {
    name = 'UpdateFravarOperationsType1753789040000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update existing operations to change Operasjonstype from individual types to "Fravær"
        // and update descriptions to match PGS-monitor format
        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'Fravær', 
                "BeskrivelseMal" = 'Fraværstatus satt til "Dokumentert fravær" i PGS-monitor.'
            WHERE "OperationID" = 100;
        `);

        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'Fravær', 
                "BeskrivelseMal" = 'Fraværstatus satt til "Ikke-dokumentert fravær" i PGS-monitor.'
            WHERE "OperationID" = 103;
        `);

        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'Fravær', 
                "BeskrivelseMal" = 'Fraværstatus ble opphevet i PGS-monitor.'
            WHERE "OperationID" = 104;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Restore original values
        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'DokumentertFravær', 
                "BeskrivelseMal" = 'Fraværstatus satt til ''Dokumentert fravær''.'
            WHERE "OperationID" = 100;
        `);

        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'UdokumentertFravær', 
                "BeskrivelseMal" = 'Fraværstatus satt til ''Udokumentert fravær''.'
            WHERE "OperationID" = 103;
        `);

        await queryRunner.query(`
            UPDATE "Operation" 
            SET "Operasjonstype" = 'FraværOpphevet', 
                "BeskrivelseMal" = 'Fraværstatus opphevet.'
            WHERE "OperationID" = 104;
        `);
    }
}
