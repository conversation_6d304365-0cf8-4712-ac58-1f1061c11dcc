"use client";

import { useActionState } from "react";
import { useEffect, useRef, useState, useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { checkAuthorized } from "@/app/lib/checkAuthorized";
import { RouteEnum } from "@/app/enums/RouteEnum";
import { useSignalR } from "@/app/context/signalRContext";
import { SignalRMessageEnum } from "@/app/enums/SignalRMessageEnum";
import { AlertCircle, Loader2, RefreshCw, Send } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { logEvent } from "@/app/lib/appInsightsClient";
import { IGotAccessMessage } from "@/app/interfaces/IGotAccessMessage";
import { IAccessDeniedMessage } from "@/app/interfaces/IAccessDeniedMessage";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { get } from "http";
import { getRoute } from "@/app/lib/getRoute";

interface IGotAccessLabelProps {
  needAccessLabel: string;
  alreadyAccessLabel: string;
  gotAccessLabel: string;
  noAccessLabel: string;
  controlCodeLabel: string;
  messageSentLabel: string;
  dontRefreshLabel: string;
  connectionErrorLabel?: string;
  tryAgainLabel?: string;
  connectingLabel?: string;
  accessDeniedLabel?: string;
  accessDeniedMessageLabel?: string;
  sendNewRequestLabel?: string;
  sessionId: string;
}

type RequestStatus = "initial" | "sending" | "sent";

export default function GotAccess({
  needAccessLabel,
  alreadyAccessLabel,
  messageSentLabel,
  dontRefreshLabel,
  connectionErrorLabel = "Tilkoblingsfeil",
  tryAgainLabel = "Prøv igjen",
  connectingLabel = "Kobler til...",
  accessDeniedLabel = "Tilgang avslått",
  accessDeniedMessageLabel = "Din forespørsel om tilgang til eksamen er avslått. Ta kontakt med en eksamensvakt for mer informasjon.",
  sendNewRequestLabel = "Send ny forespørsel",
  sessionId,
}: IGotAccessLabelProps) {
  const initialState = { requestId: "", error: "" };
  const [state, formAction] = useActionState(checkAuthorized, initialState);
  const [isPending, startTransition] = useTransition();
  const [requestStatus, setRequestStatus] = useState<RequestStatus>("initial");
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [accessDenied, setAccessDenied] = useState(false);
  const router = useRouter();
  const { connection, connectionState, reconnect } = useSignalR();
  const isMounted = useRef(true);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef(new AbortController());
  const maxRetries = 3;
  const [retryCount, setRetryCount] = useState(0);

  const handleRetry = useCallback(() => {
    if (!isMounted.current) return;

    abortControllerRef.current.abort();
    abortControllerRef.current = new AbortController();

    setShowError(false);
    setErrorMessage("");
    reconnect();
    setRequestStatus("initial");
    setRetryCount((prev) => prev + 1);
  }, [reconnect]);

  const handleSendNewRequest = useCallback(async () => {
    if (!isMounted.current) return;

    // Reset states to allow a new request without page refresh
    setAccessDenied(false);
    setShowError(false);
    setErrorMessage("");
    setRequestStatus("initial");
    setRetryCount(0);

    // Reset the form state
    const formData = new FormData();

    try {
      // Send a new access request immediately
      setRequestStatus("sending");
      startTransition(() => {
        formAction(formData);
      });

      if (isMounted.current) {
        setRequestStatus("sent");
      }
    } catch (error) {
      if (isMounted.current) {
        console.error("Error sending new access request:", error);
        setShowError(true);
        setErrorMessage(
          error instanceof Error
            ? error.message
            : "Ukjent feil ved sending av forespørsel"
        );
        setRequestStatus("initial");
      }
    }
  }, [formAction]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      abortControllerRef.current.abort();
    };
  }, []);

  useEffect(() => {
    if (!connection) return;

    const handleOnline = () => {
      if (connectionState === "FAILED") {
        handleRetry();
      }
    };

    const handleConnectionSlow = () => {
      if (isMounted.current) {
        setErrorMessage("Tilkoblingen er treg - vennligst vent...");
      }
    };

    window.addEventListener("online", handleOnline);
    connection.on("ConnectionSlow", handleConnectionSlow);

    return () => {
      window.removeEventListener("online", handleOnline);
      connection.off("ConnectionSlow", handleConnectionSlow);
    };
  }, [connection, connectionState, handleRetry]);

  // Auto-retry on connection error with backoff
  useEffect(() => {
    if (
      connectionState === "FAILED" &&
      requestStatus === "initial" &&
      retryCount < maxRetries &&
      !accessDenied
    ) {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      const backoffTime = Math.min(2000 * Math.pow(2, retryCount), 10000);

      retryTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          console.log(
            `Auto-retry attempt ${
              retryCount + 1
            }/${maxRetries} after ${backoffTime}ms`
          );
          handleRetry();
        }
      }, backoffTime);
    }
  }, [connectionState, requestStatus, retryCount, handleRetry, accessDenied]);

  // SignalR message handler
  useEffect(() => {
    if (!connection) return;

    const handleGotAccess = async (message: IGotAccessMessage) => {
      if (!isMounted.current) return;

      if (message?.id === sessionId) {
        // Session ID matches - proceed to exam
        await Promise.all([
          logEvent("signalRGotAccessValidated", {
            userSessionId: message.id,
            candidateNumber: message.candidateNumber,
          }),
          logActivity("", "", 0, OperationEnum.AutorisertFraMonitor, {
            side: "eksamensoppgavesiden",
          }),
        ]);

        router.push(RouteEnum.Eksamensoppgave);
      }
    };

    const handleAccessDenied = (message: IAccessDeniedMessage) => {
      if (!isMounted.current) return;

      if (message?.id === sessionId) {
        // Session ID matches - show access denied message
        logEvent("signalRAccessDenied", {
          userSessionId: message.id,
          candidateNumber: message.candidateNumber,
        });
        console.log("AccessDenied message received:", message);
        setAccessDenied(true);
        setRequestStatus("initial");
      }
    };

    connection.on(SignalRMessageEnum.GotAccess, handleGotAccess);
    connection.on(SignalRMessageEnum.AccessDenied, handleAccessDenied);

    return () => {
      connection.off(SignalRMessageEnum.GotAccess, handleGotAccess);
      connection.off(SignalRMessageEnum.AccessDenied, handleAccessDenied);
    };
  }, [connection, router, sessionId]);

  // Send access request when connected
  useEffect(() => {
    if (
      connectionState === "CONNECTED" &&
      !state?.requestId &&
      requestStatus === "initial" &&
      isMounted.current &&
      !accessDenied
    ) {
      const sendAccessRequest = async () => {
        const { signal } = abortControllerRef.current;

        try {
          if (signal.aborted) return;

          setRequestStatus("sending");
          const formData = new FormData();
          if (signal.aborted) return;

          startTransition(() => {
            formAction(formData);
          });

          if (isMounted.current && !signal.aborted) {
            setRequestStatus("sent");
            setRetryCount(0);
          }
        } catch (error) {
          if (isMounted.current && !signal.aborted) {
            console.error("Error sending access request:", error);
            setShowError(true);
            setErrorMessage(
              error instanceof Error
                ? error.message
                : "Ukjent feil ved sending av forespørsel"
            );
            setRequestStatus("initial");
          }
        }
      };

      const timer = setTimeout(sendAccessRequest, 500);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [
    connectionState,
    formAction,
    state?.requestId,
    requestStatus,
    accessDenied,
    startTransition,
  ]);

  // Handle errors from formAction
  useEffect(() => {
    if (state.error && isMounted.current) {
      setShowError(true);
      setErrorMessage(state.error);
      setRequestStatus("initial");
    }
  }, [state?.error]);

  // Render connection status
  const renderConnectionStatus = () => {
    if (connectionState === "CONNECTING") {
      return (
        <div className="flex items-center gap-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="font-medium">{connectingLabel}</span>
        </div>
      );
    }

    if (connectionState === "FAILED" || showError) {
      return (
        <Alert className="border-red-200 bg-red-50 rounded-lg">
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <AlertTitle className="font-semibold text-red-800">
                {connectionErrorLabel}
              </AlertTitle>
            </div>
            <AlertDescription className="text-red-700 flex items-center gap-10 justify-between">
              <span>
                {errorMessage ||
                  state?.error ||
                  "Kunne ikke sende tilgangsforespørsel. Sjekk internettilkoblingen din og prøv igjen."}
              </span>
              <Button
                size="sm"
                variant="destructive"
                className="w-fit gap-2"
                onClick={handleRetry}
              >
                <RefreshCw className="w-4 h-4" />
                {tryAgainLabel}
              </Button>
            </AlertDescription>
          </div>
        </Alert>
      );
    }

    return null;
  };

  // Render access denied message
  const renderAccessDenied = () => {
    if (!accessDenied) return null;

    return (
      <Alert className="border-amber-200 bg-amber-50 rounded-lg">
        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-amber-600" />
            <AlertTitle className="font-semibold text-amber-800">
              {accessDeniedLabel}
            </AlertTitle>
          </div>
          <AlertDescription className="text-amber-700">
            {accessDeniedMessageLabel}
          </AlertDescription>
          <div className="flex justify-end">
            <Button
              size="sm"
              variant="outline"
              className="gap-2 border-amber-300 text-amber-800 hover:bg-amber-100 hover:text-amber-900 hover:border-amber-400"
              onClick={handleSendNewRequest}
            >
              <Send className="w-4 h-4" />
              {sendNewRequestLabel}
            </Button>
          </div>
        </div>
      </Alert>
    );
  };

  // Render request status
  const renderRequestStatus = () => {
    if (accessDenied) {
      return renderAccessDenied();
    }

    if (
      !state.requestId &&
      (requestStatus === "sending" ||
        (connectionState === "CONNECTED" && requestStatus === "initial"))
    ) {
      return (
        <div className="flex flex-col items-center gap-2">
          <div className="flex items-center gap-2">
            <Loader2 className="w-5 h-5 animate-spin text-primary" />
            <span className="text-gray-900">Sender tilgangsforespørsel...</span>
          </div>
        </div>
      );
    }

    if (state.requestId) {
      return (
        <div className="animate-in fade-in slide-in-from-bottom-4 duration-500 ease-out">
          <div className="flex flex-col items-center gap-2 bg-[#FFF3E0] rounded-sm p-4 border-[#EF6C00] border-2">
            <div className="flex items-center gap-2">
              <span className="text-gray-900">{messageSentLabel}</span>
            </div>
            <span className="text-sm text-gray-700">{dontRefreshLabel}</span>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <Card className="rounded-none w-full bg-white min-h-56">
      <CardHeader>
        <CardTitle className="text-gray-900">{needAccessLabel}</CardTitle>
        <p className="text-gray-700">{alreadyAccessLabel}</p>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center gap-4 min-h-24">
          {renderConnectionStatus()}
          {renderRequestStatus()}
        </div>
      </CardContent>
    </Card>
  );
}
