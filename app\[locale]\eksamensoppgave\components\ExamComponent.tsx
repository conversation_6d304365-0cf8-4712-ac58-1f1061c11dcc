"use client";

import { getLanguage } from "@/app/lib/getLanguage";
import { Button } from "@/components/ui/button";
import { getBlob, getBlobIos } from "@/app/lib/getBlob";
import { toast } from "@/components/ui/use-toast";
import { BiArrowToBottom } from "react-icons/bi";
import PreviewBtn from "./PreviewBtn";
import { logEvent } from "@/app/lib/appInsightsClient";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { getTestPart } from "@/app/lib/getTestPart";

interface exerciseProps {
  paperType: string;
  downloadtxt: string;
  previewTxt: string;
  malformTxt: string;
  previewHoverLabel: string;
  item: IExercise;
  previousLabel: string;
  nextLabel: string;
  errorLoadingExerciseLabel: string;
  subjectCode: string;
  testPartId?: number;
  preparation?: boolean;
}

export default function ExamComponent({
  item,
  paperType,
  downloadtxt,
  previewTxt,
  malformTxt,
  previewHoverLabel,
  previousLabel,
  nextLabel,
  errorLoadingExerciseLabel,
  subjectCode,
  testPartId,
  preparation,
}: exerciseProps) {
  const handleDownload = async () => {
    try {
      await logEvent("examFileDownload", {
        GenFileName: item.GenFileName,
        FileName: item.OrgFileName,
        SubjectCode: subjectCode,
      });

      let result;

      await logActivity(
        item.GenFileName,
        getTestPart(testPartId ?? 0),
        testPartId ?? 0,
        preparation
          ? OperationEnum.LastetNedPreparation
          : OperationEnum.LastetNedOppgave
      );

      if (
        /iPad|iPhone|iPod/.test(navigator.userAgent) ||
        (navigator.userAgent.includes("Mac") && "ontouchend" in document)
      ) {
        await getBlobIos(item.GenFileName, item.OrgFileName, "oppgaver", true);
      } else {
        await getBlob(item.GenFileName, item.OrgFileName, "oppgaver", true);
      }
    } catch (error) {
      console.error("Feil ved nedlasting av fil:", error);
      toast({
        variant: "destructive",
        title: errorLoadingExerciseLabel,
      });
    }
  };

  return (
    <div className="card rounded-none w-full bg-white">
      <div className="card-body flex">
        <div className="flex flex-col sm:flex-row">
          <h2 className="card-title font-normal text-base">
            <span>
              {item.SubjectCode} - {paperType}
            </span>
          </h2>
          <span className="text-sm text-info flex gap-2 sm:ml-auto">
            {malformTxt}: {getLanguage(item.LanguageVariant)}
          </span>
        </div>

        <div className="flex lg:flex-row md:flex-row flex-col mt-4 gap-4 max-w-max">
          <Button
            type="submit"
            variant="outline"
            onClick={async () => {
              await handleDownload();
            }}
            className="flex rounded-[3px] border-2 h-12 normal-case w-full sm:w-48"
          >
            <span className="flex gap-2 font-semibold flex items-center">
              <BiArrowToBottom
                className=""
                role="img"
                aria-label="Nedlastingsikon"
                size="22px"
              />
              {downloadtxt}
            </span>
          </Button>

          <PreviewBtn
            previewHoverLabel={previewHoverLabel}
            fileExtension={item.FileExtension}
            previewTxt={previewTxt}
            genFileName={item.GenFileName}
            orgFileName={item.OrgFileName}
            errorLoadingExerciseLabel={errorLoadingExerciseLabel}
            subjectCode={subjectCode}
            testPartId={testPartId}
            preparation={preparation}
          />
        </div>
      </div>
    </div>
  );
}
