import dayjs from "dayjs";
import { CandidateStatusEnum } from "../enums/CandidateStatusEnum";
import { RouteEnum } from "../enums/RouteEnum";
import { IStatusInfo } from "../interfaces/IStatusInfo";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import { isCandidateBlocked } from "./isCandidateBlocked";

export const getRoute = async (
  finalStatus: IStatusInfo,
  userInfoFromRedis: IUserSessionData
) => {
  try {
    if (!finalStatus || !userInfoFromRedis) {
      return "autoriseringssiden";
    }

    const isBlocked = await isCandidateBlocked(
      finalStatus.UserId,
      finalStatus.SchoolId
    );

    if (
      isBlocked &&
      ![
        CandidateStatusEnum.Levert,
        CandidateStatusEnum.LevertManuelt,
        CandidateStatusEnum.DokumentertFravaer,
        CandidateStatusEnum.IkkeDokumentertFravaer,
      ].includes(finalStatus.Status)
    ) {
      return "Ingen tilgang siden";
    }

    const examStarted = dayjs
      .utc()
      .isAfter(dayjs.utc(finalStatus.TestStartTime));

    switch (finalStatus.Status) {
      case CandidateStatusEnum.IkkeInnlogget:
      case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
        return "autoriseringssiden";
      case CandidateStatusEnum.InnloggetAutentisert:
      case CandidateStatusEnum.LastetOpp:
        if (userInfoFromRedis && !userInfoFromRedis.isAuthorized)
          return "autoriseringssiden";
        else if (!examStarted) return "ventesiden";
        else return "eksamensoppgavesiden";
      case CandidateStatusEnum.Levert:
      case CandidateStatusEnum.LevertManuelt:
        return "kvitteringssiden";
      case CandidateStatusEnum.DokumentertFravaer:
      case CandidateStatusEnum.IkkeDokumentertFravaer:
      case CandidateStatusEnum.SkalLeverePaPapir:
        return "fraværssiden";
      case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
      case CandidateStatusEnum.IkkeInnlogget:
        return "autoriseringssiden";

      default:
        return "autoriseringssiden";
    }
  } catch (error) {
    console.error("Error in getRoute:", error);
    return "";
  }
};
