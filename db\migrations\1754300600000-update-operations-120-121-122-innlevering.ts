import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperations120121122Innlevering1754300600000 implements MigrationInterface {
    name = 'UpdateOperations120121122Innlevering1754300600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 120 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert via siden "Lever for kandidat".'
            WHERE "OperationID" = 120;
        `);

        // Update operation 121 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert for del 1 via siden "Lever for kandidat".'
            WHERE "OperationID" = 121;
        `);

        // Update operation 122 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert for del 2 via siden "Lever for kandidat".'
            WHERE "OperationID" = 122;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert operation 120 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert.'
            WHERE "OperationID" = 120;
        `);

        // Revert operation 121 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert for del 1.'
            WHERE "OperationID" = 121;
        `);

        // Revert operation 122 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Filen ble innlevert for del 2.'
            WHERE "OperationID" = 122;
        `);
    }
}
