import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperations147148149150Besvarelsesdel1754300900000 implements MigrationInterface {
    name = 'AddOperations147148149150Besvarelsesdel1754300900000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operations 147-150
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (147, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Filen fikk endret eksamensdel til del 1 via siden "Lever for kandidat". Filen ble endret til både del 1 og del 2.'),
            (148, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Filen fikk endret eksamensdel til del 2 via siden "Lever for kandidat". Filen ble endret til både del 1 og del 2.'),
            (149, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Filen fikk endret eksamensdel til del 1 via gruppeopplasteren. Filen ble endret til både del 1 og del 2.'),
            (150, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Filen fikk endret eksamensdel til del 2 via gruppeopplasteren. Filen ble endret til både del 1 og del 2.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operations
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" IN (147, 148, 149, 150);
        `);
    }
}
