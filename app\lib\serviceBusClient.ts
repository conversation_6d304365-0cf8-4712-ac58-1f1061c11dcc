// lib/serviceBus.ts
import {
  ServiceBusClient,
  ServiceBusSender,
  ServiceBusReceiver,
  ServiceBusClientOptions,
  RetryMode,
} from "@azure/service-bus";
import { DefaultAzureCredential } from "@azure/identity";
import { AuditLogColumns } from "../enums/AuditLogColumns";
import Bow<PERSON> from "bowser";
import { IAuditLogData } from "../interfaces/IAuditLogData";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

const connectionString = process.env.SERVICE_BUS_CONNECTION_STRING;
const serviceBusNamespace = process.env.SERVICE_BUS_NAMESPACE; // e.g., "myservicebus.servicebus.windows.net"
const queueName = process.env.SERVICE_BUS_QUEUE_NAME;

// Validate required environment variables
if (!connectionString && !serviceBusNamespace) {
  console.warn(
    "Service Bus not configured: Missing SERVICE_BUS_CONNECTION_STRING or SERVICE_BUS_NAMESPACE"
  );
}

if (!queueName) {
  console.warn("Service Bus not configured: Missing SERVICE_BUS_QUEUE_NAME");
}

// Global storage for persistent connection across requests
declare global {
  var __serviceBusClient: ServiceBusClient | undefined;
  var __serviceBusSenderCache: Map<string, ServiceBusSender> | undefined;
  var __serviceBusReceiverCache: Map<string, ServiceBusReceiver> | undefined;
}

// Connection pooling limits for memory management
const MAX_SENDERS = 10;
const MAX_RECEIVERS = 5;

// Connection pooling and caching with global persistence and limits
let sbClient: ServiceBusClient | null = global.__serviceBusClient || null;
const senderCache =
  global.__serviceBusSenderCache || new Map<string, ServiceBusSender>();
const receiverCache =
  global.__serviceBusReceiverCache || new Map<string, ServiceBusReceiver>();

// Store in global for persistence
global.__serviceBusSenderCache = senderCache;
global.__serviceBusReceiverCache = receiverCache;

// Configuration for high traffic
const CLIENT_OPTIONS: ServiceBusClientOptions = {
  retryOptions: {
    maxRetries: 5,
    maxRetryDelayInMs: 30000,
    mode: RetryMode.Exponential,
  },
  // Using WebSocket transport with compression and large payload support
  webSocketOptions: {
    webSocketConstructorOptions: {
      perMessageDeflate: true,
      maxPayload: 100 * 1024 * 1024, // 100MB max payload
    },
  },
};

// Lazy initialization with error handling and global persistence
export function getServiceBusClient(): ServiceBusClient {
  if (!sbClient) {
    try {
      if (connectionString) {
        console.log("Initializing Service Bus with connection string");
        sbClient = new ServiceBusClient(connectionString, CLIENT_OPTIONS);
      } else if (serviceBusNamespace) {
        console.log("Initializing Service Bus with managed identity");
        const credential = new DefaultAzureCredential();
        sbClient = new ServiceBusClient(
          serviceBusNamespace,
          credential,
          CLIENT_OPTIONS
        );
      } else {
        throw new Error(
          "Either SERVICE_BUS_CONNECTION_STRING (for localhost) or SERVICE_BUS_NAMESPACE (for Azure) must be provided"
        );
      }

      // Store in global for persistence across requests
      global.__serviceBusClient = sbClient;
    } catch (error) {
      console.error("Failed to initialize Service Bus client:", error);
      throw error;
    }
  }
  return sbClient;
}

// Cached sender with connection reuse, validation, and memory limits
export function getSender(queueNameParam?: string): ServiceBusSender {
  const targetQueue = queueNameParam || queueName;

  if (!targetQueue) {
    throw new Error(
      "Queue name is required but not provided in environment variables or parameters"
    );
  }

  if (!senderCache.has(targetQueue)) {
    // Check cache size limits to prevent memory leaks
    if (senderCache.size >= MAX_SENDERS) {
      // Remove oldest sender (first entry)
      const firstKey = senderCache.keys().next().value;
      if (firstKey) {
        const oldSender = senderCache.get(firstKey);
        if (oldSender) {
          oldSender
            .close()
            .catch((err) => console.error("Error closing old sender:", err));
        }
        senderCache.delete(firstKey);
        console.log(
          `Removed old sender for queue: ${firstKey} to prevent memory overflow`
        );
      }
    }

    const client = getServiceBusClient();
    const sender = client.createSender(targetQueue);
    senderCache.set(targetQueue, sender);
    console.log(`Created Service Bus sender for queue: ${targetQueue}`);
  }

  return senderCache.get(targetQueue)!;
}

// Cached receiver with connection reuse and memory limits
export function getReceiver(
  queueNameParam?: string,
  receiveMode: "peekLock" | "receiveAndDelete" = "peekLock"
): ServiceBusReceiver {
  const targetQueue = queueNameParam || queueName;
  if (!targetQueue) {
    throw new Error(
      "Queue name is required but not provided in environment variables or parameters"
    );
  }
  const cacheKey = `${targetQueue}_${receiveMode}`;

  if (!receiverCache.has(cacheKey)) {
    // Check cache size limits to prevent memory leaks
    if (receiverCache.size >= MAX_RECEIVERS) {
      // Remove oldest receiver (first entry)
      const firstKey = receiverCache.keys().next().value;
      if (firstKey) {
        const oldReceiver = receiverCache.get(firstKey);
        if (oldReceiver) {
          oldReceiver
            .close()
            .catch((err) => console.error("Error closing old receiver:", err));
        }
        receiverCache.delete(firstKey);
        console.log(
          `Removed old receiver for key: ${firstKey} to prevent memory overflow`
        );
      }
    }

    const client = getServiceBusClient();
    const receiver = client.createReceiver(targetQueue, { receiveMode });
    receiverCache.set(cacheKey, receiver);
  }

  return receiverCache.get(cacheKey)!;
}

// Browser info cache for performance optimization
const browserCache = new Map<
  string,
  {
    platform: string;
    os: string;
    browser: string;
    version: string;
  }
>();

// Failed audit logs queue for retry mechanism
const failedAuditLogs: Array<{
  data: IAuditLogData;
  userAgent: string;
  timestamp: number;
  retryCount: number;
}> = [];

// Cache browser parsing results
function getCachedBrowserInfo(userAgent: string) {
  if (!browserCache.has(userAgent)) {
    const browser = Bowser.getParser(userAgent);
    browserCache.set(userAgent, {
      platform: browser.getPlatformType(),
      os: browser.getOSName(),
      browser: browser.getBrowserName(),
      version: browser.getBrowserVersion(),
    });
  }
  return browserCache.get(userAgent)!;
}

// Build audit log message (extracted for reuse)
function buildAuditLogMessage(
  data: IAuditLogData,
  browserInfo: any
): Record<string, any> {
  return {
    [AuditLogColumns.KANDIDAT_PAMELDING_ID]: data.kandidatpaameldingId,
    [AuditLogColumns.TIMESTAMP]: dayjs.utc().toISOString(),
    [AuditLogColumns.ROLLE]: data.rolle,
    [AuditLogColumns.KANDIDAT_NR]: data.kandidatNr,
    [AuditLogColumns.KANDIDAT_FORNAVN]: data.kandidatFornavn,
    [AuditLogColumns.KANDIDAT_ETTERNAVN]: data.kandidatEtternavn,
    [AuditLogColumns.IP]: data.ip,
    [AuditLogColumns.DEVICE]: browserInfo.platform,
    [AuditLogColumns.OS]: browserInfo.os,
    [AuditLogColumns.BROWSER]: browserInfo.browser,
    [AuditLogColumns.BROWSER_EDITION]: browserInfo.version,
    [AuditLogColumns.OPERATION_ID]: data.operationId || null,
    [AuditLogColumns.PARAMETERS]: data.parameters || null,
    [AuditLogColumns.EKSAMENSDEL]: data.eksamensdel || null,
    [AuditLogColumns.FILNAVN]: data.filnavn || null,
    [AuditLogColumns.DEL1]:
      data.eksamensdel && data.eksamensdel === "Eksamen del 1"
        ? data.eksamensdel
        : null,
    [AuditLogColumns.DEL2]:
      data.eksamensdel && data.eksamensdel === "Eksamen del 2"
        ? data.eksamensdel
        : null,
  };
}

// Configuration for memory management
const MAX_FAILED_LOGS = 1000; // Maximum number of failed logs to keep in memory
const MAX_LOG_AGE_MS = 24 * 60 * 60 * 1000; // 24 hours

// Store failed audit log for retry with memory limits
async function storeAuditLogForRetry(
  data: IAuditLogData,
  userAgent: string,
  error: any
): Promise<void> {
  // Clean up old logs before adding new ones
  cleanupOldFailedLogs();

  // Check memory limits
  if (failedAuditLogs.length >= MAX_FAILED_LOGS) {
    // Remove oldest logs to make space
    const logsToRemove = Math.floor(MAX_FAILED_LOGS * 0.1); // Remove 10%
    failedAuditLogs.splice(0, logsToRemove);
    console.warn(
      `Removed ${logsToRemove} old failed logs to prevent memory overflow`
    );
  }

  failedAuditLogs.push({
    data,
    userAgent,
    timestamp: Date.now(),
    retryCount: 0,
  });

  // Log the failure for monitoring
  console.error("Audit log failed, stored for retry:", {
    operationId: data.operationId,
    error: error.message,
    queueSize: failedAuditLogs.length,
  });
}

// Cleanup old failed logs to prevent memory leaks
function cleanupOldFailedLogs(): void {
  const now = Date.now();
  const initialLength = failedAuditLogs.length;

  // Remove logs older than MAX_LOG_AGE_MS
  for (let i = failedAuditLogs.length - 1; i >= 0; i--) {
    if (now - failedAuditLogs[i].timestamp > MAX_LOG_AGE_MS) {
      failedAuditLogs.splice(i, 1);
    }
  }

  const removedCount = initialLength - failedAuditLogs.length;
  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} old failed audit logs`);
  }
}

// Batch sending for high throughput
export async function sendMessagesBatch(
  messages: any[],
  batchSize: number = 100
) {
  const sender = getSender();
  const batches = [];

  // Split messages into batches
  for (let i = 0; i < messages.length; i += batchSize) {
    batches.push(messages.slice(i, i + batchSize));
  }

  const results = await Promise.allSettled(
    batches.map(async (batch) => {
      try {
        const messageBatch = await sender.createMessageBatch();

        for (const message of batch) {
          if (!messageBatch.tryAddMessage(message)) {
            // If message doesn't fit, send current batch and create new one
            if (messageBatch.count > 0) {
              await sender.sendMessages(messageBatch);
            }
            const newBatch = await sender.createMessageBatch();
            if (!newBatch.tryAddMessage(message)) {
              throw new Error(`Message too large: ${JSON.stringify(message)}`);
            }
            await sender.sendMessages(newBatch);
          }
        }

        if (messageBatch.count > 0) {
          await sender.sendMessages(messageBatch);
        }

        return { success: true, count: batch.length };
      } catch (error) {
        console.error("Batch send error:", error);
        return { success: false, error, count: batch.length };
      }
    })
  );

  return results;
}

// Health check function
export async function healthCheck(): Promise<{
  healthy: boolean;
  details: any;
}> {
  try {
    // Simple health check by initializing client
    getServiceBusClient();

    return {
      healthy: true,
      details: {
        clientInitialized: !!sbClient,
        sendersActive: senderCache.size,
        receiversActive: receiverCache.size,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      healthy: false,
      details: {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
    };
  }
}

// ASYNC LOGGING FUNCTIONS

/**
 * Async audit logging with timeout - Fire and forget pattern for non-critical operations
 * Does not block the calling function and has built-in timeout protection
 */
export function sendAuditLogAsync(
  data: IAuditLogData,
  userAgent: string,
  context?: string,
  timeoutMs: number = 5000 // 5 second default timeout
): void {
  // Validate inputs before async processing
  if (!data || !userAgent) {
    console.error(
      `Invalid audit log data for async send [${context || "unknown"}]`
    );
    return;
  }

  // Use setImmediate to ensure this runs after current execution context
  setImmediate(async () => {
    try {
      console.log(
        `Starting async audit log for operation: ${data.operationId} [${
          context || "unknown"
        }]`
      );

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(
          () => reject(new Error(`Audit log timeout after ${timeoutMs}ms`)),
          timeoutMs
        );
      });

      // Race between audit log send and timeout
      await Promise.race([
        sendAuditLogToServiceBus(data, userAgent),
        timeoutPromise,
      ]);

      console.log(
        `Completed async audit log for operation: ${data.operationId} [${
          context || "unknown"
        }]`
      );
    } catch (error) {
      console.error(`Async audit log failed [${context || "unknown"}]:`, error);
      // Store for retry without blocking
      try {
        await storeAuditLogForRetry(data, userAgent, error);
      } catch (retryError) {
        console.error(
          `Failed to store audit log for retry [${context || "unknown"}]:`,
          retryError
        );
      }
    }
  });
}

/**
 * Safe audit logging with configurable behavior, timeout, and retry mechanism
 * @param data - Audit log data
 * @param userAgent - User agent string
 * @param options - Configuration options
 * @returns Promise<boolean> - Success status
 */
export async function sendAuditLogSafe(
  data: IAuditLogData,
  userAgent: string,
  options: {
    blocking?: boolean;
    fallback?: boolean;
    context?: string;
    maxRetries?: number;
    timeoutMs?: number;
  } = {}
): Promise<boolean> {
  const maxRetries = options.maxRetries || 3;
  const timeoutMs = options.timeoutMs || 100; // Default 100ms timeout for blocking operations
  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      if (options.blocking) {
        // Synchronous - wait for completion with retry and timeout
        console.log(
          `Sending audit log attempt ${attempt}/${maxRetries} [${
            options.context || "unknown"
          }]`
        );

        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(
            () =>
              reject(
                new Error(`Blocking audit log timeout after ${timeoutMs}ms`)
              ),
            timeoutMs
          );
        });

        // Race between audit log send and timeout
        await Promise.race([
          sendAuditLogToServiceBus(data, userAgent),
          timeoutPromise,
        ]);

        console.log(
          `Audit log sent successfully on attempt ${attempt} [${
            options.context || "unknown"
          }]`
        );
        return true;
      } else {
        // Asynchronous - fire and forget with timeout
        sendAuditLogAsync(data, userAgent, options.context, options.timeoutMs);
        return true;
      }
    } catch (error) {
      lastError = error;
      console.error(
        `Audit log attempt ${attempt}/${maxRetries} failed [${
          options.context || "unknown"
        }]:`,
        error
      );

      if (attempt < maxRetries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // All retries failed
  console.error(
    `All ${maxRetries} audit log attempts failed [${
      options.context || "unknown"
    }]:`,
    lastError
  );

  if (options.fallback) {
    try {
      await storeAuditLogForRetry(data, userAgent, lastError);
      console.log(
        `Audit log stored for background retry [${
          options.context || "unknown"
        }]`
      );
    } catch (fallbackError) {
      console.error(
        `Failed to store audit log for retry [${
          options.context || "unknown"
        }]:`,
        fallbackError
      );
    }
  }

  return false;
}

/**
 * Batch multiple audit logs for better performance with timeout
 */
export async function sendAuditLogsBatch(
  logs: Array<{ data: IAuditLogData; userAgent: string }>,
  timeoutMs: number = 10000 // 10 second timeout for batch operations
): Promise<void> {
  const messages = logs.map(({ data, userAgent }) => {
    const browserInfo = getCachedBrowserInfo(userAgent);
    const auditLog = buildAuditLogMessage(data, browserInfo);
    return {
      body: JSON.stringify(auditLog),
      contentType: "application/json",
    };
  });

  // Create timeout promise
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(
      () => reject(new Error(`Batch audit log timeout after ${timeoutMs}ms`)),
      timeoutMs
    );
  });

  // Race between batch send and timeout
  await Promise.race([sendMessagesBatch(messages), timeoutPromise]);
}

/**
 * RECOMMENDED: Async-first audit logging function
 * This should be the default choice for most use cases
 * Non-blocking with proper error handling and retry mechanism
 */
export function logAuditAsync(
  data: IAuditLogData,
  userAgent: string,
  context?: string
): void {
  sendAuditLogAsync(data, userAgent, context, 5000); // 5 second timeout
}

/**
 * CRITICAL OPERATIONS ONLY: Blocking audit logging with short timeout
 * Only use this for operations where audit logging failure should block the operation
 * Has a very short timeout to prevent performance issues
 */
export async function logAuditBlocking(
  data: IAuditLogData,
  userAgent: string,
  context?: string,
  timeoutMs: number = 100 // Very short timeout for blocking operations
): Promise<boolean> {
  return sendAuditLogSafe(data, userAgent, {
    blocking: true,
    context,
    timeoutMs,
    maxRetries: 1, // Only one retry for blocking operations
  });
}

// ORIGINAL SYNCHRONOUS FUNCTION (kept for critical operations) - Now with circuit breaker
export async function sendAuditLogToServiceBus(
  data: IAuditLogData,
  userAgent: string
): Promise<void> {
  try {
    // Validate required data
    if (!data || !userAgent) {
      throw new Error("Missing required audit log data or user agent");
    }

    const browserInfo = getCachedBrowserInfo(userAgent);
    const auditLog = buildAuditLogMessage(data, browserInfo);

    const message = {
      body: JSON.stringify(auditLog),
      contentType: "application/json",
    };

    console.log(
      `Sending audit log for operation: ${data.operationId} to queue: ${queueName}`
    );

    // Use circuit breaker for better reliability
    await sendMessageWithCircuitBreaker(message);

    console.log(
      `Successfully sent audit log for operation: ${data.operationId}`
    );
  } catch (error) {
    console.error("Failed to send audit log to Service Bus:", error);
    throw error;
  }
}

// Periodic cleanup function to prevent memory leaks
export function performPeriodicCleanup(): void {
  try {
    // Clean up old failed logs
    cleanupOldFailedLogs();
  } catch (error) {
    console.error("Error during periodic cleanup:", error);
  }
}

// Auto-cleanup every 30 minutes
if (typeof setInterval !== "undefined") {
  setInterval(performPeriodicCleanup, 30 * 60 * 1000);
}

// Graceful shutdown with cleanup
export async function gracefulShutdown(): Promise<void> {
  console.log("Starting Service Bus graceful shutdown...");

  try {
    // Clear retry interval
    if (retryInterval) {
      clearInterval(retryInterval);
      retryInterval = null;
    }

    // Process any remaining failed logs one last time
    await processFailedAuditLogs();

    // Close all senders
    const senderPromises = Array.from(senderCache.values()).map(
      async (sender) => {
        try {
          await sender.close();
        } catch (error) {
          console.error("Error closing sender:", error);
        }
      }
    );

    // Close all receivers
    const receiverPromises = Array.from(receiverCache.values()).map(
      async (receiver) => {
        try {
          await receiver.close();
        } catch (error) {
          console.error("Error closing receiver:", error);
        }
      }
    );

    await Promise.all([...senderPromises, ...receiverPromises]);

    // Clear caches and failed logs
    senderCache.clear();
    receiverCache.clear();
    failedAuditLogs.length = 0; // Clear failed logs array

    // Close client
    if (sbClient) {
      await sbClient.close();
      sbClient = null;
    }

    console.log("Service Bus graceful shutdown completed");
  } catch (error) {
    console.error("Error during graceful shutdown:", error);
    throw error;
  }
}

// RETRY MECHANISM FOR FAILED AUDIT LOGS

/**
 * Process failed audit logs with exponential backoff
 */
async function processFailedAuditLogs(): Promise<void> {
  if (failedAuditLogs.length === 0) return;

  const now = Date.now();
  const logsToRetry = failedAuditLogs.filter((log) => {
    const timeSinceFailure = now - log.timestamp;
    const backoffDelay = Math.min(1000 * Math.pow(2, log.retryCount), 30000); // Max 30 seconds
    return timeSinceFailure >= backoffDelay && log.retryCount < 5; // Max 5 retries
  });

  for (const logEntry of logsToRetry) {
    try {
      await sendAuditLogToServiceBus(logEntry.data, logEntry.userAgent);

      // Remove successful retry from queue
      const index = failedAuditLogs.indexOf(logEntry);
      if (index > -1) {
        failedAuditLogs.splice(index, 1);
      }

      console.log(
        `Successfully retried audit log for operation: ${logEntry.data.operationId}`
      );
    } catch (error) {
      // Increment retry count
      logEntry.retryCount++;
      logEntry.timestamp = now;

      if (logEntry.retryCount >= 5) {
        // Remove from queue after max retries
        const index = failedAuditLogs.indexOf(logEntry);
        if (index > -1) {
          failedAuditLogs.splice(index, 1);
        }
        console.error(`Audit log permanently failed after 5 retries:`, {
          operationId: logEntry.data.operationId,
          error: error,
        });
      }
    }
  }
}

// Schedule periodic retry processing (every 30 seconds)
let retryInterval: NodeJS.Timeout | null = null;

function scheduleRetryProcessing(): void {
  if (!retryInterval) {
    retryInterval = setInterval(processFailedAuditLogs, 30000);
  }
}

// Start retry processing
scheduleRetryProcessing();

// UTILITY FUNCTIONS FOR DEBUGGING

/**
 * Test Service Bus connection and configuration
 */
export async function testServiceBusConnection(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log("Testing Service Bus connection...");

    // Check environment variables
    if (!connectionString && !serviceBusNamespace) {
      return {
        success: false,
        message:
          "Missing SERVICE_BUS_CONNECTION_STRING or SERVICE_BUS_NAMESPACE",
      };
    }

    if (!queueName) {
      return {
        success: false,
        message: "Missing SERVICE_BUS_QUEUE_NAME",
      };
    }

    // Test client initialization
    getServiceBusClient();
    console.log("Service Bus client initialized successfully");

    // Test sender creation
    getSender();
    console.log(`Service Bus sender created for queue: ${queueName}`);

    return {
      success: true,
      message: "Service Bus connection test successful",
      details: {
        queueName,
        hasConnectionString: !!connectionString,
        hasNamespace: !!serviceBusNamespace,
      },
    };
  } catch (error) {
    console.error("Service Bus connection test failed:", error);
    return {
      success: false,
      message: `Service Bus connection test failed: ${error}`,
      details: error,
    };
  }
}

/**
 * Get Service Bus status and metrics
 */
export function getServiceBusStatus(): {
  isInitialized: boolean;
  senderCacheSize: number;
  receiverCacheSize: number;
  failedLogsCount: number;
  configuration: any;
} {
  return {
    isInitialized: !!sbClient,
    senderCacheSize: senderCache.size,
    receiverCacheSize: receiverCache.size,
    failedLogsCount: failedAuditLogs.length,
    configuration: {
      hasConnectionString: !!connectionString,
      hasNamespace: !!serviceBusNamespace,
      queueName: queueName || "NOT_SET",
    },
  };
}

// Utility function for circuit breaker pattern
class CircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private readonly failureThreshold = 5,
    private readonly timeout = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === "OPEN") {
      if (Date.now() - this.lastFailTime > this.timeout) {
        this.state = "HALF_OPEN";
      } else {
        throw new Error("Circuit breaker is OPEN");
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = "CLOSED";
  }

  private onFailure() {
    this.failures++;
    this.lastFailTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = "OPEN";
    }
  }
}

const circuitBreaker = new CircuitBreaker();

// Enhanced send with circuit breaker and error handling
export async function sendMessageWithCircuitBreaker(message: any) {
  return circuitBreaker.execute(async () => {
    const sender = getSender();
    try {
      await sender.sendMessages(message);
    } catch (error) {
      // Clear sender cache on connection errors to force recreation
      if (isConnectionError(error)) {
        senderCache.clear();
      }
      throw error;
    }
  });
}

// Send message with automatic retry and cache invalidation
export async function sendMessage(message: any, queueNameParam?: string) {
  const targetQueue = queueNameParam || queueName;

  if (!targetQueue) {
    throw new Error("Queue name is required but not provided");
  }

  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    try {
      const sender = getSender(targetQueue);
      await sender.sendMessages(message);
      return; // Success
    } catch (error) {
      attempts++;
      console.error(`Send attempt ${attempts} failed:`, error);

      // Clear cache on connection errors
      if (isConnectionError(error)) {
        senderCache.delete(targetQueue);
        if (attempts < maxAttempts) {
          await delay(Math.pow(2, attempts) * 1000); // Exponential backoff
        }
      } else {
        throw error; // Non-connection errors shouldn't retry
      }
    }
  }

  throw new Error(`Failed to send message after ${maxAttempts} attempts`);
}

// Helper function to identify connection errors
function isConnectionError(error: any): boolean {
  if (!error) return false;
  const errorMessage = error.message?.toLowerCase() || "";
  const errorCode = error.code || "";

  return (
    errorMessage.includes("connection") ||
    errorMessage.includes("socket") ||
    errorMessage.includes("timeout") ||
    errorCode === "ECONNRESET" ||
    errorCode === "ENOTFOUND"
  );
}

// Helper delay function
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Process exit handler for cleanup
if (typeof process !== "undefined") {
  const handleExit = () => {
    gracefulShutdown().catch(console.error);
  };

  process.on("SIGTERM", handleExit);
  process.on("SIGINT", handleExit);
  process.on("beforeExit", handleExit);
}
