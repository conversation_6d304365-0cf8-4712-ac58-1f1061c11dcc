// app/api/signalr/route.ts
import { AuditLogColumns } from "@/app/enums/AuditLogColumns";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { getUserLoggingInfo } from "@/app/lib/getUserLoggingInfo";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import {
  logAuditAsync,
} from "@/app/lib/serviceBusClient";
import AuditLogService from "@/db/services/auditLogService";
import Bowser from "bowser";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface ILogActivity {
  FileName?: string;
  ExamPartId?: string;
  TestPartId?: number;
  ActivityName: OperationEnum;
  ActivityDescription?: string; // For legacy support
  Parameters?: Record<string, any>; // For new structure
}

export async function POST(request: NextRequest) {
  try {
    const userSessionData: IUserSessionData = await getUserSessionData();

    if (!userSessionData.userSessionId)
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });

    const payload: ILogActivity = await request.json();    

    const data = await getUserLoggingInfo(
      payload.ActivityName,
      payload.Parameters,
      payload.ExamPartId,    
      payload.FileName ? payload.FileName : "",
      userSessionData
    );

    if (data) {
      // Send to Service Bus asynchronously (non-blocking) - RECOMMENDED APPROACH
      const headersList = await headers();
      const userAgent = headersList.get("user-agent") || "Unknown User Agent";
      logAuditAsync(data, userAgent, "logActivity");

      // No need to wait or check success - fire and forget with retry mechanism
    } else {
      console.error(
        "Failed to build audit log data for activity:",
        payload.ActivityName
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error logging activity:", error);

    return NextResponse.json({ success: true });
  }
}
