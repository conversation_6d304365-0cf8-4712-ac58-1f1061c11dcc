import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperation105Description1751774400000 implements MigrationInterface {
    name = 'UpdateOperation105Description1751774400000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 105 description from "Satt som 'Sendes i posten' i kandidatmonitoren." 
        // to "Fremdriftsstatus satt til "Sendes i posten" i PGS-monitor."
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Fremdriftsstatus satt til "Sendes i posten" i PGS-monitor.'
            WHERE "OperationID" = 105;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to original description
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Satt som ''Sendes i posten'' i kandidatmonitoren.'
            WHERE "OperationID" = 105;
        `);
    }
}
