import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDel1Del2ColumnsToAuditlog1753792849018 implements MigrationInterface {
    name = 'AddDel1Del2ColumnsToAuditlog1753792849018'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add Del1 column to AuditLog table (nullable)
        await queryRunner.query(`
            ALTER TABLE "AuditLog"
            ADD "Del1" varchar(255) NULL
        `);

        // Add Del2 column to AuditLog table (nullable)
        await queryRunner.query(`
            ALTER TABLE "AuditLog"
            ADD "Del2" varchar(255) NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove Del2 column from AuditLog table
        await queryRunner.query(`
            ALTER TABLE "AuditLog"
            DROP COLUMN "Del2"
        `);

        // Remove Del1 column from AuditLog table
        await queryRunner.query(`
            ALTER TABLE "AuditLog"
            DROP COLUMN "Del1"
        `);
    }
}
