"use client";

import { OperationEnum } from "../enums/OperationEnum";

//TODO: Legge til feature toggle for aktivering og deaktivering av aktivitet logging
export async function logActivity(
  filename: string,
  examPartId: string,
  testPartId: number,
  activityName: OperationEnum,
  parameters?: Record<string, any>
) {
  try {
    const response = await fetch(`/api/logActivity`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        FileName: filename,
        ExamPartId: examPartId,
        TestPartId: testPartId,
        ActivityName: activityName,
        Parameters: parameters,
      }),
    });

    if (!response.ok) {
      console.error(
        `Failed to log activity. Status: ${response.status}. Message: ${response.statusText}`
      );
    }
  } catch (error) {
    console.error("An error occurred while logging activity:", error);
  }
}
