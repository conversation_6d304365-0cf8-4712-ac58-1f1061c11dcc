import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperation108Description1751774500000 implements MigrationInterface {
    name = 'UpdateOperation108Description1751774500000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 108 description from "Satt som 'Ikke sendes i posten' i kandidatmonitoren." 
        // to "Angret fremdriftsstatus "Sendes i posten" i PGS-monitor."
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Angret fremdriftsstatus "Sendes i posten" i PGS-monitor.'
            WHERE "OperationID" = 108;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to original description
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Satt som ''Ikke sendes i posten'' i kandidatmonitoren.'
            WHERE "OperationID" = 108;
        `);
    }
}
