import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDigitalAccessClosedOperations1753516800000 implements MigrationInterface {
    name = 'AddDigitalAccessClosedOperations1753516800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operations for digital access closure scenarios
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (135, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Levert digitalt" ble digital tilgang stengt.'),
            (136, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Levert digitalt" for del 2 ble digital tilgang stengt.'),
            (137, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Sendes i posten" ble digital tilgang stengt.'),
            (138, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Sendes i posten" for del 1 ble digital tilgang stengt.'),
            (139, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Sendes i posten" for del 2 ble digital tilgang stengt.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the operations added in the up migration
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" IN (135, 136, 137, 138, 139);
        `);
    }
}
