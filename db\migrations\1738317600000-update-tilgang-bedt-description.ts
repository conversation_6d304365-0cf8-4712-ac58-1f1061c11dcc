import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTilgangBedtDescription1738317600000 implements MigrationInterface {
    name = 'UpdateTilgangBedtDescription1738317600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 2 (TilgangBedt) description to include reason
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'PGS ba automatisk om tilgang for kandidaten fordi hen kom til autorisasjonssiden.'
            WHERE "OperationID" = 2;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert operation 2 description to original
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'PGS ba automatisk om tilgang for kandidaten.'
            WHERE "OperationID" = 2;
        `);
    }
}
