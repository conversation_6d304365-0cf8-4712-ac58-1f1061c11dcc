import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLogoutOperations1753603200000 implements MigrationInterface {
    name = 'AddLogoutOperations1753603200000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operations for logout scenarios when digital access is blocked/removed
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (140, 'Utlogging', 'Som følge av at kandidaten fikk digital tilgang sperret ble kandidaten logget ut.'),
            (141, 'Utlogging', 'Som følge av at kandidaten fikk digital tilgang fjernet ble kandidaten logget ut.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the operations added in the up migration
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" IN (140, 141);
        `);
    }
}
