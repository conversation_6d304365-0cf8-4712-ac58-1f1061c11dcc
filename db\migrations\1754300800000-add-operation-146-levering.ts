import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperation146Levering1754300800000 implements MigrationInterface {
    name = 'AddOperation146Levering1754300800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operation 146
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (146, 'Levering', 'PGS oppdaterte fremdriftsstatus automatisk til "Levert digitalt" for del 1.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operation
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" = 146;
        `);
    }
}
