"use client";

import {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useSession } from "next-auth/react";
import { HubConnection, HubConnectionState } from "@microsoft/signalr";
import { getSignalRConnection } from "../lib/signalRService";

interface SignalRContextValue {
  connection: HubConnection | undefined;
  connectionState: ConnectionState;
  lastError: Error | null;
  reconnect: () => Promise<void>;
}

type ConnectionState =
  | "CONNECTING"
  | "CONNECTED"
  | "DISCONNECTED"
  | "FAILED"
  | "RECONNECTING";

const SignalRContext = createContext<SignalRContextValue>({
  connection: undefined,
  connectionState: "DISCONNECTED",
  lastError: null,
  reconnect: async () => {},
});

export const SignalRProvider = ({ children }: { children: ReactNode }) => {
  const [connection, setConnection] = useState<HubConnection | undefined>();
  const [connectionState, setConnectionState] =
    useState<ConnectionState>("DISCONNECTED");
  const [lastError, setLastError] = useState<Error | null>(null);
  const retryCount = useRef(0);
  const maxRetries = 5;
  const abortController = useRef(new AbortController());
  const isMounted = useRef(true);
  const connectionRef = useRef<HubConnection | undefined>(undefined);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const cleanupConnection = useCallback(async () => {
    const conn = connectionRef.current;
    if (!conn) return;

    try {
      conn.off("close");
      conn.off("reconnected");
      conn.off("reconnecting");
      conn.off("Connected");

      if (conn.state !== HubConnectionState.Disconnected) {
        await conn.stop();
      }
    } catch (error) {
      console.error("Error during connection cleanup:", error);
    }
  }, []);

  const cleanupReconnectTimer = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  const attemptReconnectRef = useRef<(() => void) | null>(null);

  const setupConnectionHandlers = useCallback((conn: HubConnection) => {
    conn.off("close");
    conn.off("reconnected");
    conn.off("reconnecting");

    conn.onclose(async (error) => {
      console.log("Connection closed:", error);
      if (isMounted.current) {
        setConnectionState("DISCONNECTED");
        attemptReconnectRef.current?.();
      }
    });

    conn.onreconnecting((error) => {
      console.log("Connection reconnecting:", error);
      if (isMounted.current) {
        setConnectionState("RECONNECTING");
        setLastError(error as Error);
      }
    });

    conn.onreconnected(() => {
      console.log("Connection reestablished");
      if (isMounted.current) {
        setConnectionState("CONNECTED");
        setLastError(null);
        retryCount.current = 0;
        cleanupReconnectTimer();
      }
    });

    conn.on("Connected", () => {
      console.log("Connection established");
      if (isMounted.current) {
        setConnectionState("CONNECTED");
        setLastError(null);
        retryCount.current = 0;
        cleanupReconnectTimer();
      }
    });
  }, [cleanupReconnectTimer]);

  const startConnection = useCallback(async (conn: HubConnection) => {
    try {
      if (conn.state === HubConnectionState.Disconnected) {
        setConnectionState("CONNECTING");
        await conn.start();
      }

      if (conn.state === HubConnectionState.Connected) {
        setConnectionState("CONNECTED");
      }
    } catch (error) {
      console.error("Failed to start connection:", error);
      if (isMounted.current) {
        setConnectionState("FAILED");
        setLastError(error as Error);
      }
      throw error;
    }
  }, []);

  const attemptReconnect = useCallback(() => {
    if (
      !isMounted.current ||
      connectionState === "CONNECTING" ||
      connectionState === "RECONNECTING"
    ) {
      return;
    }

    if (retryCount.current >= maxRetries) {
      console.log("Max retries reached, giving up");
      if (isMounted.current) {
        setConnectionState("FAILED");
      }
      return;
    }

    retryCount.current += 1;
    const delay = Math.min(1000 * Math.pow(2, retryCount.current), 30000);

    console.log(`Attempting reconnect #${retryCount.current} in ${delay}ms`);

    cleanupReconnectTimer();
    
    reconnectTimeoutRef.current = setTimeout(async () => {
      const doReconnect = async () => {
        try {
          if (!isMounted.current || abortController.current.signal.aborted) return;

          abortController.current.abort();
          abortController.current = new AbortController();

          await cleanupConnection();
          cleanupReconnectTimer();

          if (abortController.current.signal.aborted) return;

          const conn = await getSignalRConnection();
          
          if (abortController.current.signal.aborted) {
            await conn.stop();
            return;
          }

          setupConnectionHandlers(conn);
          connectionRef.current = conn;
          setConnection(conn);
          setConnectionState("RECONNECTING");
          await startConnection(conn);
        } catch (error) {
          console.error("Reconnect failed:", error);
          if (isMounted.current && !abortController.current.signal.aborted) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            
            if (errorMessage.includes("connection was stopped during negotiation")) {
              console.log("Connection stopped during negotiation, stopping reconnect attempts");
              setConnectionState("DISCONNECTED");
              return;
            }
            
            setConnectionState("FAILED");
            setLastError(error as Error);
            
            setTimeout(() => {
              if (isMounted.current) {
                attemptReconnect();
              }
            }, 2000);
          }
        }
      };

      await doReconnect();
    }, delay);
  }, [connectionState, cleanupReconnectTimer, cleanupConnection, setupConnectionHandlers, startConnection]);

  useEffect(() => {
    attemptReconnectRef.current = attemptReconnect;
  }, [attemptReconnect]);

  const initializeConnection = useCallback(async (): Promise<void> => {
    if (!isMounted.current) return;

    try {
      abortController.current.abort();
      abortController.current = new AbortController();

      await cleanupConnection();
      cleanupReconnectTimer();

      if (abortController.current.signal.aborted) {
        return;
      }

      const conn = await getSignalRConnection();
      
      if (abortController.current.signal.aborted) {
        await conn.stop();
        return;
      }

      setupConnectionHandlers(conn);
      connectionRef.current = conn;
      setConnection(conn);
      await startConnection(conn);
    } catch (error) {
      console.error("Connection attempt failed:", error);
      if (isMounted.current && !abortController.current.signal.aborted) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (errorMessage.includes("connection was stopped during negotiation")) {
          console.log("Connection stopped during negotiation, not setting to FAILED");
          setConnectionState("DISCONNECTED");
          return;
        }
        
        setConnectionState("FAILED");
        setLastError(error as Error);
      }
    }
  }, [cleanupConnection, cleanupReconnectTimer, setupConnectionHandlers, startConnection]);

  const handleReconnect = useCallback(async () => {
    if (!isMounted.current) return;

    retryCount.current = 0;
    cleanupReconnectTimer();
    await initializeConnection();
  }, [initializeConnection, cleanupReconnectTimer]);

  const { status } = useSession();

  useEffect(() => {
    const handleOnline = () => {
      if (
        isMounted.current &&
        (connectionState === "FAILED" || connectionState === "DISCONNECTED")
      ) {
        handleReconnect();
      }
    };

    window.addEventListener("online", handleOnline);
    return () => window.removeEventListener("online", handleOnline);
  }, [connectionState, handleReconnect]);

  useEffect(() => {
    isMounted.current = true;
    if (status === "authenticated") {
      initializeConnection();
    }
    return () => {
      isMounted.current = false;
      abortController.current.abort();
      cleanupConnection();
      cleanupReconnectTimer();
    };
  }, [initializeConnection, cleanupConnection, cleanupReconnectTimer, status]);

  return (
    <SignalRContext.Provider
      value={{
        connection,
        connectionState,
        lastError,
        reconnect: handleReconnect,
      }}
    >
      {children}
    </SignalRContext.Provider>
  );
};

export const useSignalR = () => {
  const context = useContext(SignalRContext);
  if (!context) {
    throw new Error("useSignalR must be used within a SignalRProvider");
  }
  return context;
};
