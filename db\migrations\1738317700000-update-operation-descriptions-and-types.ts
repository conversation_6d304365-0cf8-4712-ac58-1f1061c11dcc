import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperationDescriptionsAndTypes1738317700000 implements MigrationInterface {
    name = 'UpdateOperationDescriptionsAndTypes1738317700000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation types and descriptions
        await queryRunner.query(`
            UPDATE "Operation"
            SET 
                "Operasjonstype" = CASE "OperationID"
                    WHEN 11 THEN 'Oppgave'
                    WHEN 25 THEN 'Kvittering'
                    ELSE "Operasjonstype"
                END,
                "BeskrivelseMal" = CASE "OperationID"
                    WHEN 1 THEN 'Kandidaten logget inn - ny sesjon startet. Kandidaten kom til {side}.'
                    WHEN 4 THEN 'Kandidaten ble autorisert i PGS-monitor. Kandidaten kom til {side}.'
                    WHEN 19 THEN 'Kandidaten bekreftet levering. Totalt {antallFiler} filer ble innlevert.'
                    WHEN 20 THEN 'PGS oppdaterte fremdriftsstatus automatisk til "<PERSON><PERSON> digitalt".'
                    WHEN 22 THEN 'PGS oppdaterte fremdriftsstatus automatisk til "Levert digitalt" for del 2.'
                    WHEN 23 THEN 'Kandidaten logget ut. Innloggingssesjonen ble avsluttet.'
                    WHEN 25 THEN 'Kandidaten lastet ned kvittering.'
                    WHEN 100 THEN 'Fraværstatus satt til "Dokumentert fravær" i PGS-monitor.'
                    WHEN 101 THEN 'Kandidaten fikk tilgang fra PGS-monitor.'
                    WHEN 102 THEN 'Kandidaten sin tilgangsforespørsel ble avvist i PGS-monitor.'
                    WHEN 103 THEN 'Fraværstatus satt til "Ikke-dokumentert fravær" i PGS-monitor.'
                    WHEN 104 THEN 'Fraværstatus ble opphevet i PGS-monitor.'
                    WHEN 106 THEN 'Fremdriftsstatus for del 1 satt til "Sendes i posten" i PGS-monitor.'
                    WHEN 107 THEN 'Fremdriftsstatus for del 2 satt til "Sendes i posten" i PGS-monitor.'
                    WHEN 109 THEN 'Angret fremdriftsstatus "Sendes i posten" for del 1 i PGS-monitor.'
                    WHEN 110 THEN 'Angret fremdriftsstatus "Sendes i posten" for del 2 i PGS-monitor.'
                    WHEN 111 THEN 'Fremdriftsstatus satt til "Levert digitalt" i PGS-monitor via valget "Sett som levert digitalt".'
                    WHEN 112 THEN 'Fremdriftsstatus for del 2 satt til "Levert digitalt" i PGS-monitor via valget "Sett som levert digitalt".'
                    WHEN 114 THEN 'Del 2 ble åpnet for ny levering via PGS-monitor.'
                    WHEN 115 THEN 'Kandidaten fikk digitalt tilgang sperret fra PGS-monitor.'
                    WHEN 116 THEN 'Digital sperre ble opphevet fra PGS-monitor.'
                    WHEN 118 THEN 'Filen ble slettet via siden "Lever for kandidat".'
                    WHEN 119 THEN 'Filen ble opplastet via siden "Lever for kandidat".'
                    WHEN 125 THEN 'Filen ble markert som sjekket via siden "Lever for kandidat".'
                    WHEN 126 THEN 'Kandidaten fikk fjernet tilgang fra PGS-monitor.'
                    ELSE "BeskrivelseMal"
                END
            WHERE "OperationID" IN (1, 4, 11, 19, 20, 22, 23, 25, 100, 101, 102, 103, 104, 106, 107, 109, 110, 111, 112, 114, 115, 116, 118, 119, 125, 126);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert operation types and descriptions to original values
        await queryRunner.query(`
            UPDATE "Operation"
            SET 
                "Operasjonstype" = CASE "OperationID"
                    WHEN 11 THEN 'Nedlasting'
                    WHEN 25 THEN 'Nedlasting'
                    ELSE "Operasjonstype"
                END,
                "BeskrivelseMal" = CASE "OperationID"
                    WHEN 1 THEN 'Kandidaten logget inn - ny sesjon startet. Kandidaten kom til {side}'
                    WHEN 4 THEN 'Kandidaten fikk tilgang fra kandidatmonitoren. Kandidaten kom til {side}'
                    WHEN 19 THEN 'Kandidaten bekreftet levering. Fremdriftsstatus satt til Levert digitalt. Totalt {antallFiler} filer er levert.'
                    WHEN 20 THEN 'PGS satte status automatisk til Levert digitalt. Digital tilgang er stengt.'
                    WHEN 22 THEN 'PGS satte status automatisk til Levert digitalt for del 2. Digital tilgang er stengt.'
                    WHEN 23 THEN 'Kandidaten logget ut. Innloggingssesjonen ble avsluttet'
                    WHEN 25 THEN 'Kandidaten lastet ned kvittering.'
                    WHEN 100 THEN 'Fraværstatus satt til ''Dokumentert fravær''.'
                    WHEN 101 THEN 'Kandidaten fikk tilgang fra kandidatmonitoren.'
                    WHEN 102 THEN 'Kandidaten sin tilgangsforespørsel ble avvist i kandidatmonitoren.'
                    WHEN 103 THEN 'Fraværstatus satt til ''Ikke-dokumentert fravær''.'
                    WHEN 104 THEN 'Fraværstatus opphevet.'
                    WHEN 106 THEN 'Del 1 satt som ''Sendes i posten'' i kandidatmonitoren.'
                    WHEN 107 THEN 'Del 2 satt som ''Sendes i posten'' i kandidatmonitoren.'
                    WHEN 109 THEN 'Del 1 satt som ''Ikke sendes i posten'' i kandidatmonitoren'
                    WHEN 110 THEN 'Del 2 satt som ''Ikke sendes i posten'' i kandidatmonitoren'
                    WHEN 111 THEN 'Satt som ''Levert digitalt'' i kandidatmonitoren.'
                    WHEN 112 THEN 'Del 2 satt som ''Levert digitalt'' i kandidatmonitoren.'
                    WHEN 114 THEN '{partNumber} ble åpnet for ny levering.'
                    WHEN 115 THEN 'Kandidaten fikk digitalt tilgang sperret.'
                    WHEN 116 THEN 'Digital sperre ble opphevet.'
                    WHEN 118 THEN 'Filen ble slettet.'
                    WHEN 119 THEN 'Filen ble lastet opp.'
                    WHEN 125 THEN 'Filen ble markert som sjekket.'
                    WHEN 126 THEN 'Kandidaten fikk fjernet digital tilgang fra monitoren.'
                    ELSE "BeskrivelseMal"
                END
            WHERE "OperationID" IN (1, 4, 11, 19, 20, 22, 23, 25, 100, 101, 102, 103, 104, 106, 107, 109, 110, 111, 112, 114, 115, 116, 118, 119, 125, 126);
        `);
    }
}
