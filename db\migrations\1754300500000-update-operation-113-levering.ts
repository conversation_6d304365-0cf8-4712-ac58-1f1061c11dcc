import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperation113Levering1754300500000 implements MigrationInterface {
    name = 'UpdateOperation113Levering1754300500000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 113 to use "Levering" as operation type
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'Levering'
            WHERE "OperationID" = 113;
        `);

        // Update operation 113 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Eksamen ble åpnet for ny levering via PGS-monitor.'
            WHERE "OperationID" = 113;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert operation 113 back to previous values
        await queryRunner.query(`
            UPDATE "Operation"
            SET "Operasjonstype" = 'ÅpnetForNyLeveringEksamen'
            WHERE "OperationID" = 113;
        `);

        // Revert operation 113 description template
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Eksamen ble åpnet for ny levering.'
            WHERE "OperationID" = 113;
        `);
    }
}
