import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperation26FilenInnlevert20250804140703000 implements MigrationInterface {
    name = 'AddOperation26FilenInnlevert20250804140703000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operation 26 for FilenInnlevert (enum value 26)
        // This corresponds to OperationEnum.FilenInnlevert = 26
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (26, 'Innlevering', 'Filen ble innlevert.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operation
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" = 26;
        `);
    }
}
