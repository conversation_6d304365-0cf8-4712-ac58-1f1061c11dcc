"use client";

import { getSession, signOut } from "next-auth/react";
import { stopSignalRConnection } from "./signalRService";
import { logActivity } from "./logActivity";
import { OperationEnum } from "../enums/OperationEnum";

export async function federatedlogout(sessionId?: string) {
  const session: any = await getSession();

  try {
    await fetch(`${window.location.origin}/api/removeUserFromSignalRGroups`, {
      method: "DELETE",
    });

    // Pass sessionId to deauthorize only specific session if provided
    const deauthorizeBody = sessionId
      ? JSON.stringify({ sessionId })
      : undefined;
    await fetch(`${window.location.origin}/api/deauthorize`, {
      method: "POST",
      headers: sessionId
        ? {
            "Content-Type": "application/json",
          }
        : {},
      body: deauthorizeBody,
    });

    await logActivity("", "", 0, OperationEnum.LoggetUt);
  } catch (error) {
    console.error(
      "Error during removeUserFromSignalRGroups/deauthorize :",
      error
    );
  }

  const queryParams = { idtoken: session?.user?.idToken };
  const queryString = new URLSearchParams(queryParams);

  const response = await fetch(
    `${window.location.origin}/api/federatedlogout?${queryString}`
  );
  const data = await response.json();

  if (response.ok) {
    try {
      await stopSignalRConnection();
    } catch (error) {}

    await signOut({ redirect: false });
    window.location.href = data.url;
  }
}
