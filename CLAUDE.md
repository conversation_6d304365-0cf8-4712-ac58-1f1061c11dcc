# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database Migrations
Database migrations use TypeORM with environment-specific configurations:

```bash
# Show migration status
npm run migration:show:local
npm run migration:show:dev
npm run migration:show:test
npm run migration:show:qa
npm run migration:show:production

# Run migrations
npm run migration:run:local
npm run migration:run:dev
npm run migration:run:test
npm run migration:run:qa
npm run migration:run:production

# Revert migrations
npm run migration:revert:local
npm run migration:revert:dev
npm run migration:revert:test
npm run migration:revert:qa
npm run migration:revert:production
```

Alternative PowerShell script approach:
```powershell
.\run-migrations.ps1 -Action show -Environment local
.\run-migrations.ps1 -Action run -Environment local
.\run-migrations.ps1 -Action revert -Environment local
.\run-migrations.ps1 -Action generate -Environment local -Name "MigrationName"
```

## Architecture Overview

### Project Structure
- **app/** - Next.js 13+ app router structure
  - **[locale]/** - Internationalized pages (nb/nn locales)
  - **api/** - API routes including auth, file operations, SignalR
  - **lib/** - Shared utilities and helper functions
  - **interfaces/** - TypeScript interfaces
  - **enums/** - TypeScript enums
  - **hooks/** - Custom React hooks
- **db/** - Database layer with TypeORM
  - **models/** - Database entities (AuditLog, Operation)
  - **migrations/** - Database migrations with environment-specific configs
  - **services/** - Database service layer
- **components/** - Reusable UI components
  - **ui/** - Shadcn/ui components

### Key Technologies
- **Next.js 15** with app router
- **TypeORM** for database operations with SQL Server
- **NextAuth.js** with Duende Identity Server 6
- **next-intl** for internationalization (Norwegian Bokmål/Nynorsk)
- **Tailwind CSS** + **NextUI** + **Radix UI** for styling
- **SignalR** for real-time communication
- **Azure services** (Service Bus, Blob Storage, Identity)
- **Redis** for session management

### Authentication & Authorization
- Uses NextAuth.js with Duende Identity Server 6 provider
- Role-based authorization with candidate validation
- Session management through Redis
- Middleware handles access control and route protection
- Configuration in `app/api/auth/authOptions.ts`

### Database Configuration
- TypeORM with SQL Server
- Environment-specific data sources in `db/migrations/environments/`
- Audit logging system with normalized operations
- Connection uses Azure AD authentication for non-localhost environments

### File Upload & Storage
- Azure Blob Storage integration
- SAS token-based uploads
- File type validation and size limits
- Real-time upload progress via SignalR

### Internationalization
- Norwegian Bokmål (nb) and Nynorsk (nn) support
- Locale detection from user profile
- Translation files in `translations/`
- Configuration in `middleware.ts` and `i18n.ts`

### Real-time Features
- SignalR hub for real-time updates
- File upload progress notifications
- Status change notifications
- Connection management in `app/context/signalRContext.tsx`

### Environment Configuration
Each environment requires specific `.env` files:
- `.env.local` - Local development
- `.env.dev` - Development environment
- `.env.test` - Test environment
- `.env.qa` - QA environment
- `.env.production` - Production environment

Key environment variables include database connection, NextAuth configuration, Azure service credentials, and Redis connection strings.

## Testing & Quality
- ESLint for code linting
- TypeScript for type checking
- No specific test framework configured - check codebase for testing approach before implementing tests