import { OperationEnum } from "../enums/OperationEnum";
import { IAuditLogData } from "../interfaces/IAuditLogData";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import { getClientIp } from "./getClientIp";
import { getUserSessionData } from "./getUserSessionData";
import AuditLogService from "../../db/services/auditLogService";

export async function getUserLoggingInfo(
  operationId: OperationEnum,
  parameters?: Record<string, any>,
  eksamensdel?: string, 
  filnavn?: string,
  userSessionData?: IUserSessionData
): Promise<IAuditLogData | null> {
  const userSessionoData: IUserSessionData = userSessionData
    ? userSessionData
    : await getUserSessionData();

  const auditLogService = AuditLogService.getInstance();

  const baseData = {
    kandidatpaameldingId: userSessionoData.userId,
    kandidatNr: userSessionoData.candidateNumber,
    kandidatFornavn: userSessionoData.name,
    kandidatEtternavn: userSessionoData.name,
    sesjonsId: userSessionoData.userSessionId,
    rolle:
      operationId === OperationEnum.TilgangBedt ||
      operationId === OperationEnum.StatusLevertDigitalt ||
      operationId === OperationEnum.StatusLevertDigitaltDel2
        ? "PGS"
        : "Kandidat",
    ip: await getClientIp(),
    eksamensdel: eksamensdel || "",   
    filnavn: filnavn || "",
  };

  return auditLogService.buildAuditLogData(baseData, operationId, parameters);
}
