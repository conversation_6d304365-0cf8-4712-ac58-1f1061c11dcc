"use client";

import { BiArrowToBottom } from "react-icons/bi";
import { PDFDownloadLink } from "@react-pdf/renderer";
import ReceiptDocument from "./ReceiptDocument";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

interface Props {
  downloadRecieptLabel: string;
  receiptInfo: any; // Define a proper interface for this
  submittedFiles: any; // Define a proper interface for this
}

const DownloadButton = ({
  downloadRecieptLabel,
  receiptInfo,
  submittedFiles,
}: Props) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDownload = async () => {
    await logActivity("", "", 0, OperationEnum.KvitteringNedlastet);
  };

  // Show loading button until client-side hydration is complete
  if (!isClient) {
    return (
      <Button variant="ghost" className="mb-10" disabled>
        <BiArrowToBottom size={22} role="img" aria-label="Nedlastingsikon" />
        <span>{downloadRecieptLabel}</span>
      </Button>
    );
  }

  return (
    <PDFDownloadLink
      document={
        <ReceiptDocument
          receiptInfo={receiptInfo}
          submittedFiles={submittedFiles}
        />
      }
      fileName="Kvittering.pdf"
    >
      {({ loading }) => (
        <Button
          variant="ghost"
          className="mb-10"
          onClick={handleDownload}
          disabled={loading}
        >
          <BiArrowToBottom size={22} role="img" aria-label="Nedlastingsikon" />
          <span>
            {loading ? "Laster inn dokument..." : downloadRecieptLabel}
          </span>
        </Button>
      )}
    </PDFDownloadLink>
  );
};

export default DownloadButton;
