import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOperation1InnloggingDash20250804151612000 implements MigrationInterface {
    name = 'UpdateOperation1InnloggingDash20250804151612000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update operation 1 description to use em dash (–) instead of hyphen (-)
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Kandidaten logget inn – ny sesjon startet. Kandidaten kom til {side}.'
            WHERE "OperationID" = 1;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to original description with hyphen
        await queryRunner.query(`
            UPDATE "Operation"
            SET "BeskrivelseMal" = 'Kandidaten logget inn - ny sesjon startet. Kandidaten kom til {side}.'
            WHERE "OperationID" = 1;
        `);
    }
}
