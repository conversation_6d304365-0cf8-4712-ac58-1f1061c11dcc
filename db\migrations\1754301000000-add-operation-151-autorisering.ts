import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOperation151Autorisering1754301000000 implements MigrationInterface {
    name = 'AddOperation151Autorisering1754301000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Insert new operation 151
        await queryRunner.query(`
            INSERT INTO "Operation" ("OperationID", "Operasjonstype", "BeskrivelseMal") VALUES
            (151, 'Autorisering', 'Som følge av at kandidaten fikk fremdriftsstatus "Levert digitalt" for del 1 ble digital tilgang stengt.');
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the new operation
        await queryRunner.query(`
            DELETE FROM "Operation" 
            WHERE "OperationID" = 151;
        `);
    }
}
